{"name": "freelancehub-server", "version": "1.0.0", "description": "Backend server for FreelanceHub marketplace", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest", "test:watch": "jest --watch"}, "keywords": ["freelance", "marketplace", "nodejs", "express", "mongodb"], "author": "FreelanceHub Team", "license": "MIT", "dependencies": {"express": "^4.18.2", "mongoose": "^7.5.0", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "cors": "^2.8.5", "dotenv": "^16.3.1", "helmet": "^7.0.0", "express-rate-limit": "^6.10.0", "express-mongo-sanitize": "^2.2.0", "xss-clean": "^0.1.4", "hpp": "^0.2.3", "multer": "^1.4.5-lts.1", "cloudinary": "^1.40.0", "stripe": "^13.5.0", "socket.io": "^4.7.2", "validator": "^13.11.0", "express-validator": "^7.0.1", "compression": "^1.7.4", "morgan": "^1.10.0"}, "devDependencies": {"nodemon": "^3.0.1", "jest": "^29.6.4", "supertest": "^6.3.3", "@types/jest": "^29.5.5"}, "engines": {"node": ">=16.0.0"}}